import 'dart:io';

import 'package:dio/dio.dart';

import '../api_provider/developer_api_provider.dart';
import '../response/developer_response.dart';
import '../response/generalResponse.dart';

class DeveloperRepository {
  final DeveloperApiProvider _apiProvider = DeveloperApiProvider();

  Future<DeveloperResponse> getDevelopers([int? page, int? size, String? key]) =>
      _apiProvider.getDevelopers(page, size, key);

  Future<DeveloperDetailsResponse> getDeveloperById(int id) =>
      _apiProvider.getDeveloperById(id);

  Future<GeneralResponse> addDeveloper(
    String nameAr,
    String nameEn,
    String descriptionAr,
    String descriptionEn,
    File? logo,
  ) async {
    FormData formData = FormData();

    // Add text fields
    formData.fields.add(MapEntry('name[ar]', nameAr));
    formData.fields.add(MapEntry('name[en]', nameEn));
    formData.fields.add(MapEntry('description[ar]', descriptionAr));
    formData.fields.add(MapEntry('description[en]', descriptionEn));

    // Add logo if provided
    if (logo != null) {
      formData.files.add(MapEntry(
        'developer_logo',
        await MultipartFile.fromFile(logo.path),
      ));
    }

    return _apiProvider.addDeveloper(formData);
  }

  Future<GeneralResponse> updateDeveloper(
    int id,
    String nameAr,
    String nameEn,
    String descriptionAr,
    String descriptionEn,
    File? logo,
  ) async {
    FormData formData = FormData();

    // Add ID for update
    formData.fields.add(MapEntry('id', id.toString()));
    
    // Add text fields
    formData.fields.add(MapEntry('name[ar]', nameAr));
    formData.fields.add(MapEntry('name[en]', nameEn));
    formData.fields.add(MapEntry('description[ar]', descriptionAr));
    formData.fields.add(MapEntry('description[en]', descriptionEn));

    // Add logo if provided
    if (logo != null) {
      formData.files.add(MapEntry(
        'developer_logo',
        await MultipartFile.fromFile(logo.path),
      ));
    }

    return _apiProvider.updateDeveloper(formData);
  }

  Future<GeneralResponse> deleteDeveloper(int id) =>
      _apiProvider.deleteDeveloper(id);
}
