import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../models/agent_list_model.dart';
import '../../../models/agent_model_type.dart';
import '../../../repository/agent_repository.dart';
import '../../../response/agent_list_response.dart';
import '../add_agent_page/add_agent_page.dart';
import '../agent_details_page/agent_details_page.dart';

class AgentWidgetPage extends StatefulWidget {
  @override
  _AgentWidgetPage createState() => _AgentWidgetPage();
}

class _AgentWidgetPage extends State<AgentWidgetPage> {
  List<AgentModelType> listtype = [
    AgentModelType('All Agents', true, ''),
    AgentModelType('Holiday Homes Agents', false, 'holidayHome'),
    AgentModelType('Car Rental Agents', false, 'carRent')
  ];
  bool load = false;
  String valvheck = '';
  List<AgentListModel> agentList = [];
  int pagenum = 1;
  int pagesize = 100;

  getAgetn(String val) async {
    setState(() {
      load = false;

      //error = res.error.toString();
    });
    pagenum = 1;
    final AgentRepository _repository = AgentRepository();
    pagenum == 1 ? agentList.clear() : print(pagenum);
    AgentListResponse res = await _repository.getAgent(val, pagenum, pagesize);
    print(res);

    if (res.code.toString() == '1') {
      setState(() {
        agentList.addAll(res.agentList);
        load = true;
      });
    } else {
      setState(() {
        load = true;

        //error = res.error.toString();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    getAgetn('');
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return SafeArea(
        child: Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).Agents),
      ),
      body: Stack(
        children: [
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            const SizedBox(
              height: 20,
            ),
            Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      S.of(context).AllAgents,
                      style: const TextStyle(color: Color(0xff51565B)),
                    ),
                    // GestureDetector(
                    //   onTap: () {
                    //     filterAgent(context,
                    //         valvheck: valvheck,
                    //         listtype: listtype,
                    //         load: load,
                    //         pagenum: pagenum,
                    //         agentList: agentList,
                    //         getAgetn: getAgetn);
                    //   },
                    //   child: svg2,
                    // )
                  ],
                )),
            const SizedBox(
              height: 10,
            ),
            load == false
                ? const ADLinearProgressIndicator()
                : agentList.isNotEmpty
                    ? Expanded(child: _buildCategoryWidget())
                    : SizedBox(
                        height: MediaQuery.of(context).size.height * 0.7,
                        child: Center(
                          child: Text(
                            S.of(context).Therearenoitems,
                            style: const TextStyle(fontSize: 25),
                          ),
                        ),
                      ),
            const SizedBox(
              height: 20,
            )
          ]),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: const EdgeInsets.only(
                  right: 10, left: 10, bottom: 15, top: 10),
              child: GestureDetector(
                onTap: () async {
                  Navigator.of(context)
                      .push(
                    MaterialPageRoute(builder: (_) => AddAgetnPage()),
                  )
                      .then((val) {
                    setState(() {
                      // agentList.clear();
                      load = false;
                      getAgetn('');
                    });
                  });
                },
                child: Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      color: GlobalColors.primaryColor,
                      borderRadius: BorderRadius.circular(5)),
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    child: Center(
                      child: Text(
                        S.of(context).AddAgent,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    ));
  }

  Widget _buildCategoryWidget() {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Container(
        // height: MediaQuery.of(context).size.height * 0.8,
        margin: const EdgeInsets.only(bottom: 35),
        child: ListView.builder(
          itemCount: agentList.length,
          itemBuilder: (context, index) {
            return InkWell(
              onTap: () {
                Navigator.of(context)
                    .push(
                  MaterialPageRoute(
                      builder: (_) =>
                          AgentDetailsPage(agentList[index].id.toString())),
                )
                    .then((val) {
                  setState(() {
                    load = false;
                    getAgetn('');
                  });
                });
              },
              child: Padding(
                padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                child: Container(
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 1, color: const Color(0xFFF1F1F2)),
                      borderRadius: BorderRadius.circular(5)),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
                    child: Row(
                      children: [
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFEBEBEB),
                            shape: BoxShape.circle,
                          ),
                          child: agentList[index].image == null ||
                                  agentList[index].image!.isEmpty
                              ? Padding(
                                  padding: const EdgeInsets.all(15),
                                  child: Text(
                                    agentList[index].fullname!.isEmpty
                                        ? ''
                                        : agentList[index].fullname!.length > 2
                                            ? agentList[index]
                                                .fullname!
                                                .substring(0, 2)
                                            : agentList[index].fullname![0],
                                    style: const TextStyle(
                                        color: Color(0xFFC4C4C4),
                                        fontSize: 14,
                                        fontFamily: 'Roboto-Medium'),
                                  ),
                                )
                              : ClipRRect(
                                  borderRadius: BorderRadius.circular(100),
                                  child: Image.network(
                                    agentList[index].image ?? '',
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                agentList[index].fullname ?? '',
                                style: const TextStyle(
                                    color: Color(0xFF191C1F),
                                    fontSize: 14,
                                    fontFamily: 'Roboto-Medium'),
                              ),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(0, 5, 0, 5),
                                child: Text(
                                  agentList[index].category?.name ?? '',
                                  style: const TextStyle(
                                      color: Color(0xFF8B959E),
                                      fontSize: 14,
                                      fontFamily: 'Roboto-Regular'),
                                ),
                              )
                            ],
                          ),
                        ),
                        const Spacer(),
                        const Icon(
                          Icons.keyboard_arrow_right_rounded,
                          color: Color(0xFF191C1F),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
          shrinkWrap: true,
        ),
      ),
    );
  }
}
