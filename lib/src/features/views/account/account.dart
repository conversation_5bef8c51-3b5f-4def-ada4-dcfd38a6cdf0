import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/shared_widgets/bottom_sheet_account.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/features/views/users_page/users_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../bloc/auth_blok.dart';
import '../../bloc/profile_bloc.dart';
import '../../models/multi_select_bottom_model.dart';
import '../agent_page/agent_page_widget/agent_page.dart';
import '../categories/main_categories_page.dart';
import '../featured_videos/featured_videos.dart';
import '../login/login.dart';
import '../notifications/notifications.dart';
import '../requests_and_items/request_and_items_page/requests_and_items.dart';
import 'other_settings/other_settings.dart';
import 'widgets/welcome_text_with_logo.dart';

class Account extends StatefulWidget {
  const Account({super.key});

  @override
  _Account createState() => _Account();
}

class _Account extends State<Account> {
  final Widget svg2 = SizedBox(
      width: 12,
      height: 14,
      child: SvgPicture.asset(
        'assets/Path 7025.svg',
        // semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  final Widget svg3 = SizedBox(
      width: 12,
      height: 14,
      child: SvgPicture.asset(
        'assets/icons8-video.svg',
        // semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  final Widget svg4 = SizedBox(
      width: 12,
      height: 14,
      child: SvgPicture.asset(
        'assets/Group 6937.svg',
        // semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  List<Map<String, String>> category = [
    {'icon': 'assets/Path 7024.svg', 'name': "Categories"},
    {'icon': 'assets/Path 7025.svg', 'name': "Agents"},
    {'icon': 'assets/developer.svg', 'name': "Developers"},
    {'icon': 'assets/users.svg', 'name': "Users"},
    {'icon': 'assets/icons8-video.svg', 'name': "Featured Videos"},
    {'icon': 'assets/Group 6937.svg', 'name': "Requests"},
  ];
  TextEditingController fullnameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController messageController = TextEditingController();

  List<ModelSelect> langList = [
    ModelSelect('English', AuthBloc.isEnglish),
    ModelSelect('Arabic', !(AuthBloc.isEnglish))
  ];

  @override
  void initState() {
    bloc2.getProfile();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg1 = SizedBox(
        width: 70,
        height: 70,
        child: SvgPicture.asset(
          'assets/image00002.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return SafeArea(
        child: Scaffold(
      body: SingleChildScrollView(
          child: Column(children: [
        Stack(
          children: [
            const WelcomeTextWithLogo(),
            Positioned(
              top: 80,
              left: 10,
              right: 10,
              child: SizedBox(
                  height: 130,
                  width: MediaQuery.of(context).size.width,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (BuildContext ctxt, int index) {
                      return GestureDetector(
                          onTap: () {
                            if (category[index]['name'] == 'Featured Videos') {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return FeaturedVideos();
                              }));
                            }
                            if (category[index]['name'] == 'Categories') {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return const Categories();
                              }));
                            }
                            if (category[index]['name'] == 'Agents') {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return AgentWidgetPage();
                              }));
                            }
                            if (category[index]['name'] == 'Developers') {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return DevelopersPage();
                              }));
                            }
                            if (category[index]['name'] == 'Users') {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return UserWidgetPage();
                              }));
                            }
                            if (category[index]['name'] == 'Requests') {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return const RequestsAndItemsPage();
                              }));
                            }
                          },
                          child: Container(
                              padding: const EdgeInsets.only(right: 10),
                              child: Row(children: [
                                Column(children: [
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Container(
                                      decoration: BoxDecoration(
                                          boxShadow: [
                                            BoxShadow(
                                              color: GlobalColors.primaryColor
                                                  .withOpacity(0.2),
                                              blurRadius: 10,
                                              spreadRadius: 1,
                                            )
                                          ],
                                          color: GlobalColors.secondaryColor,
                                          borderRadius:
                                              BorderRadius.circular(12)),
                                      height: 100,
                                      width: MediaQuery.of(context).size.width *
                                          0.35,
                                      padding: const EdgeInsets.only(top: 10),
                                      child: Column(children: [
                                        Center(
                                            child: Center(
                                                child: Container(
                                                    height: 50,
                                                    width: 50,
                                                    decoration: BoxDecoration(
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: GlobalColors
                                                                .primaryColor
                                                                .withOpacity(
                                                                    0.2),
                                                            blurRadius: 10,
                                                            spreadRadius: 1,
                                                          )
                                                        ],
                                                        color: GlobalColors
                                                            .primaryColor,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12)),
                                                    child: Center(
                                                        child: ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        5),
                                                            child: SizedBox(
                                                                width: category[index]
                                                                            [
                                                                            'name'] ==
                                                                        'Users'
                                                                    ? 30
                                                                    : 15,
                                                                height: category[index]
                                                                            [
                                                                            'name'] ==
                                                                        'Users'
                                                                    ? 25
                                                                    : 20,
                                                                child:
                                                                    SvgPicture
                                                                        .asset(
                                                                  category[
                                                                          index]
                                                                      ['icon']!,
                                                                  semanticsLabel:
                                                                      'Acme Logo',
                                                                  color: Colors
                                                                      .white,
                                                                  // fit: BoxFit.cover,
                                                                ))))))),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        Center(
                                          child: Builder(
                                            builder: (context) {
                                              if (category[index]['name'] ==
                                                  'Featured Videos') {
                                                return Text(
                                                    S
                                                        .of(context)
                                                        .FeaturedVideos,
                                                    style: TextStyle(
                                                        color: GlobalColors
                                                            .primaryColor,
                                                        fontSize: 13));
                                              } else if (category[index]
                                                      ['name'] ==
                                                  'Categories') {
                                                return Text(
                                                    S.of(context).Categories,
                                                    style: TextStyle(
                                                        color: GlobalColors
                                                            .primaryColor,
                                                        fontSize: 13));
                                              } else if (category[index]
                                                      ['name'] ==
                                                  'Agents') {
                                                return Text(
                                                    S.of(context).Agents,
                                                    style: TextStyle(
                                                        color: GlobalColors
                                                            .primaryColor,
                                                        fontSize: 13));
                                              } else if (category[index]
                                                      ['name'] ==
                                                  'Developers') {
                                                return Text(
                                                    S.of(context).developers,
                                                    style: TextStyle(
                                                        color: GlobalColors
                                                            .primaryColor,
                                                        fontSize: 13));
                                              } else if (category[index]
                                                      ['name'] ==
                                                  'Users') {
                                                return Text(S.of(context).Users,
                                                    style: TextStyle(
                                                        color: GlobalColors
                                                            .primaryColor,
                                                        fontSize: 13));
                                              } else {
                                                return Text(
                                                    S.of(context).Requests,
                                                    style: TextStyle(
                                                        color: GlobalColors
                                                            .primaryColor,
                                                        fontSize: 13));
                                              }
                                            },
                                          ),
                                        )
                                      ])),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                ])
                              ])));
                    },
                    itemCount: 4,
                  )),
            )
          ],
        ),
        Container(
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: Container(
              padding: const EdgeInsets.all(15),
              color: Colors.white,
              child: Column(
                children: [
                  SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: InkWell(
                          onTap: () {
                            Navigator.of(context).push(MaterialPageRoute(
                                builder: (BuildContext context) =>
                                    const OtherSettings()));
                          },
                          child: SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      S.of(context).Othersettings,
                                      style: const TextStyle(
                                          color: Color(
                                            0xff191C1F,
                                          ),
                                          // fontWeight: FontWeight.bold,
                                          fontSize: 14),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.75,
                                        child: Text(
                                          S.of(context).manage,
                                          softWrap: true,
                                          style: const TextStyle(
                                              color: Color(0xff8B959E),
                                              fontSize: 14),
                                        ))
                                  ],
                                ),
                                const Spacer(),
                                const Icon(Icons.keyboard_arrow_right,
                                    color: Color(0xff191C1F))
                              ],
                            ),
                          ))),
                  const SizedBox(
                    height: 20,
                  ),
                  InkWell(
                      onTap: () {
                        showModalBottomSheet(
                            elevation: 10,
                            context: context,
                            isScrollControlled: false,
                            backgroundColor: Colors.transparent,
                            builder: (ctx) =>
                                BottomSheetAccount(langList, 'Language'));
                      },
                      child: Row(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                      elevation: 10,
                                      context: context,
                                      isScrollControlled: false,
                                      backgroundColor: Colors.transparent,
                                      builder: (ctx) => BottomSheetAccount(
                                          langList, 'Language'));
                                },
                                child: Text(
                                  S.of(context).Language,
                                  style: const TextStyle(
                                      color: Color(
                                        0xff191C1F,
                                      ),
                                      // fontWeight: FontWeight.bold,
                                      fontSize: 14),
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Text(
                                AuthBloc.isEnglish
                                    ? S.of(context).English
                                    : S.of(context).Arabic,
                                style: const TextStyle(
                                    color: Color(0xff8B959E), fontSize: 14),
                              )
                            ],
                          ),
                          const Spacer(),
                          const Icon(Icons.keyboard_arrow_right,
                              color: Color(0xff191C1F))
                        ],
                      )),
                  const SizedBox(
                    height: 20,
                  ),
                  SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: InkWell(
                          onTap: () {
                            Navigator.of(context).push(MaterialPageRoute(
                                builder: (BuildContext context) =>
                                    const Notifications()));
                          },
                          child: Row(
                            children: [
                              InkWell(
                                  onTap: () {
                                    Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (BuildContext context) =>
                                                const Notifications()));
                                  },
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        S.of(context).Notifications,
                                        style: const TextStyle(
                                            color: Color(
                                              0xff191C1F,
                                            ),
                                            // fontWeight: FontWeight.bold,
                                            fontSize: 14),
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Text(
                                        S.of(context).TurnedOn,
                                        style: const TextStyle(
                                            color: Color(0xff8B959E),
                                            fontSize: 14),
                                      )
                                    ],
                                  )),
                              const Spacer(),
                              InkWell(
                                  onTap: () {
                                    Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (BuildContext context) =>
                                                const Notifications()));
                                  },
                                  child: const Icon(Icons.keyboard_arrow_right,
                                      color: Color(0xff191C1F)))
                            ],
                          )))
                ],
              ),
            )),
        const SizedBox(
          height: 20,
        ),
        Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Container(
              padding: const EdgeInsets.all(15),
              color: Colors.white,
              child: Column(
                children: [
                  InkWell(
                      onTap: () async {
                        SharedPreferences _prefs =
                            await SharedPreferences.getInstance();
                        _prefs.setBool('is_logged', false);
                        Navigator.of(context).pushReplacement(MaterialPageRoute(
                            builder: (BuildContext context) => const Login()));
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            S.of(context).Logout,
                            style: const TextStyle(
                                color: Color(
                                  0xffD8B77F,
                                ),
                                // fontWeight: FontWeight.bold,
                                fontSize: 14),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          const Spacer(),
                          const Icon(Icons.keyboard_arrow_right,
                              color: Color(
                                0xffD8B77F,
                              ))
                        ],
                      )),
                ],
              ),
            )),
        const SizedBox(
          height: 20,
        )
      ])),
      // bottomNavigationBar: CustomBottomNavgationBar(4),
    ));
  }
}
