import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import '../../../core/shared_widgets/ad_file_picker.dart';
import '../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../core/shared_widgets/snack_bar.dart';
import '../../models/developer_model.dart';
import '../../repository/developer_repository.dart';
import '../../response/generalResponse.dart';

class AddEditDeveloperPage extends StatefulWidget {
  final DeveloperModel? developer;

  const AddEditDeveloperPage({super.key, this.developer});

  @override
  _AddEditDeveloperPageState createState() => _AddEditDeveloperPageState();
}

class _AddEditDeveloperPageState extends State<AddEditDeveloperPage> {
  bool load = false;
  bool get isEditing => widget.developer != null;

  final TextEditingController _nameArController = TextEditingController();
  final TextEditingController _nameEnController = TextEditingController();
  final TextEditingController _descriptionArController =
      TextEditingController();
  final TextEditingController _descriptionEnController =
      TextEditingController();

  File? _logo;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _populateFields();
    }
  }

  void _populateFields() {
    final developer = widget.developer!;
    _nameArController.text = developer.nameAr ?? '';
    _nameEnController.text = developer.nameEn ?? '';
    _descriptionArController.text = developer.descriptionAr ?? '';
    _descriptionEnController.text = developer.descriptionEn ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(isEditing
              ? S.of(context).developerDetails
              : S.of(context).addNewDeveloper),
        ),
        body: Container(
          color: Colors.white,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 10),

                // Logo Upload
                ADFilePicker(
                  onSingleFileSelected: (image) => _logo = image,
                  title: S.of(context).developerLogo,
                  type: FileType.media,
                  isMultiple: false,
                ),
                const SizedBox(height: 20),

                // Arabic Name
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).developerNameAr,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey),
                          color: Colors.white,
                        ),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 15),
                          child: TextFormField(
                            controller: _nameArController,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: S.of(context).developerNameAr,
                              hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7),
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // English Name
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).developerNameEn,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey),
                          color: Colors.white,
                        ),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 15),
                          child: TextFormField(
                            controller: _nameEnController,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: S.of(context).developerNameEn,
                              hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7),
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Arabic Description
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).developerDescriptionAr,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        height: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey),
                          color: Colors.white,
                        ),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 15),
                          child: TextFormField(
                            controller: _descriptionArController,
                            maxLines: 4,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: S.of(context).developerDescriptionAr,
                              hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7),
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // English Description
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).developerDescriptionEn,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        height: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey),
                          color: Colors.white,
                        ),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 15),
                          child: TextFormField(
                            controller: _descriptionEnController,
                            maxLines: 4,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: S.of(context).developerDescriptionEn,
                              hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7),
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),

                // Submit Button
                if (load) const ADLinearProgressIndicator(),
                if (!load)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: GestureDetector(
                      onTap: _submitDeveloper,
                      child: Container(
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: GlobalColors.primaryColor,
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          child: Center(
                            child: Text(
                              isEditing
                                  ? S.of(context).save
                                  : S.of(context).addNewDeveloper,
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _submitDeveloper() async {
    setState(() {
      load = true;
    });

    final DeveloperRepository repository = DeveloperRepository();

    GeneralResponse res;
    if (isEditing) {
      res = await repository.updateDeveloper(
        widget.developer!.id!,
        _nameArController.text,
        _nameEnController.text,
        _descriptionArController.text,
        _descriptionEnController.text,
        _logo,
      );
    } else {
      res = await repository.addDeveloper(
        _nameArController.text,
        _nameEnController.text,
        _descriptionArController.text,
        _descriptionEnController.text,
        _logo,
      );
    }

    setState(() {
      load = false;
    });

    if (res.code == 1) {
      snackbar(res.msg ?? 'Success');
      Navigator.pop(context);
    } else {
      snackbar(res.msg ?? 'Error occurred');
    }
  }
}
