import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../models/developer_model.dart';
import '../../repository/developer_repository.dart';
import '../../response/developer_response.dart';
import 'add_edit_developer_page.dart';

class DevelopersPage extends StatefulWidget {
  @override
  _DevelopersPage createState() => _DevelopersPage();
}

class _DevelopersPage extends State<DevelopersPage> {
  List<DeveloperModel> developerList = [];
  bool load = false;
  final DeveloperRepository _repository = DeveloperRepository();

  @override
  void initState() {
    super.initState();
    getDevelopers('');
  }

  getDevelopers(String key) async {
    setState(() {
      load = true;
    });

    DeveloperResponse res = await _repository.getDevelopers(0, 200, key);
    if (res.code == 1) {
      setState(() {
        developerList = res.results;
        load = false;
      });
    } else {
      setState(() {
        load = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).developers),
      ),
      body: Stack(
        children: [
          Container(
            color: Colors.white,
            child: Column(
              children: [
                const SizedBox(height: 10),
                // Search Field
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white,
                    ),
                    child: TextFormField(
                      onChanged: (value) {
                        getDevelopers(value);
                      },
                      decoration: InputDecoration(
                        prefixIcon: const Icon(
                          Icons.search,
                          color: Color(0xff8B959E),
                        ),
                        contentPadding:
                            const EdgeInsets.only(left: 20, right: 20, top: 5),
                        hintText: S.of(context).searchDevelopers,
                        hintStyle:
                            const TextStyle(color: Color(0xff8B959E), fontSize: 13),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                // Developers List
                load ? const ADLinearProgressIndicator() : _buildCategoryWidget(),
              ],
            ),
          ),
          // Add Developer Button
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: const EdgeInsets.only(
                  right: 10, left: 10, bottom: 15, top: 10),
              child: GestureDetector(
                onTap: () async {
                  Navigator.of(context)
                      .push(
                    MaterialPageRoute(builder: (_) => AddEditDeveloperPage()),
                  )
                      .then((val) {
                    setState(() {
                      load = false;
                      getDevelopers('');
                    });
                  });
                },
                child: Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      color: GlobalColors.primaryColor,
                      borderRadius: BorderRadius.circular(5)),
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    child: Center(
                      child: Text(
                        S.of(context).addNewDeveloper,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    ));
  }

  Widget _buildCategoryWidget() {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Container(
        margin: const EdgeInsets.only(bottom: 35),
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: developerList.length,
          itemBuilder: (context, index) {
            return InkWell(
              onTap: () {
                Navigator.of(context)
                    .push(
                  MaterialPageRoute(
                      builder: (_) => AddEditDeveloperPage(
                          developer: developerList[index])),
                )
                    .then((val) {
                  setState(() {
                    load = false;
                    getDevelopers('');
                  });
                });
              },
              child: Container(
                padding: const EdgeInsets.only(
                    top: 10, bottom: 5, right: 20, left: 20),
                child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: GlobalColors.primaryColor.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 1,
                        )
                      ]),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      children: [
                        // Developer Logo
                        Container(
                          height: 60,
                          width: 60,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30),
                            color: GlobalColors.primaryColor.withOpacity(0.1),
                          ),
                          child: developerList[index].developerLogo != null &&
                                  developerList[index].developerLogo!.isNotEmpty
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(30),
                                  child: Image.network(
                                    developerList[index].developerLogo!,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(
                                        Icons.business,
                                        color: GlobalColors.primaryColor,
                                        size: 30,
                                      );
                                    },
                                  ),
                                )
                              : Icon(
                                  Icons.business,
                                  color: GlobalColors.primaryColor,
                                  size: 30,
                                ),
                        ),
                        const SizedBox(width: 15),
                        // Developer Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                developerList[index].name ?? '',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 5),
                              if (developerList[index].description != null)
                                Text(
                                  developerList[index].description!,
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                            ],
                          ),
                        ),
                        // Edit Icon
                        Icon(
                          Icons.edit,
                          color: GlobalColors.primaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
