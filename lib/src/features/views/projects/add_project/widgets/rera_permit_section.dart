import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/ad_file_picker.dart';

class ReraPermitSection extends HookWidget {
  final TextEditingController reraNumberController;
  final ValueNotifier<File?> reraPermitImage;

  const ReraPermitSection({
    super.key,
    required this.reraNumberController,
    required this.reraPermitImage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // RERA Number Field
        Text(
          S.of(context).reraNumber,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),
        Container(
          height: 50,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(width: 0.5, color: Colors.grey),
            color: Colors.white,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
            child: TextFormField(
              controller: reraNumberController,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: S.of(context).reraNumber,
                hintStyle: const TextStyle(
                  color: Color(0xffB7B7B7),
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // RERA Permit Image Upload
        Text(
          S.of(context).reraPermitImage,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),
        ADFilePicker(
          onSingleFileSelected: (image) => reraPermitImage.value = image,
          title: S.of(context).tabHereToUploadReraPermitImage,
          type: FileType.media,
          isMultiple: false,
        ),
        const SizedBox(height: 10),

        // Display selected image info
        if (reraPermitImage.value != null)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.green[50],
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                const Icon(Icons.image, color: Colors.green),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    S.of(context).reraPermitImageSelected,
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        const SizedBox(height: 20),
      ],
    );
  }
}
