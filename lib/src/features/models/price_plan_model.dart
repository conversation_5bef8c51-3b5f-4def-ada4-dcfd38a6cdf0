import 'package:equatable/equatable.dart';

class PricePlanDataItem extends Equatable {
  final String? date;
  final String? order;
  final int? payment;
  final String? installment;
  final String id; // Unique identifier for each item

  const PricePlanDataItem({
    this.date,
    this.order,
    this.payment,
    this.installment,
    String? id,
  }) : id = id ?? '';

  factory PricePlanDataItem.fromJson(Map<String?, dynamic> json) {
    return PricePlanDataItem(
      date: json['Date'] as String?,
      order: json['order'] as String?,
      payment: json['Payment'] as int?,
      installment: json['installment'] as String?,
      id: json['order'] as String? ?? DateTime.now().millisecondsSinceEpoch.toString(),
    );
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['Date'] = date;
    data['order'] = order;
    data['Payment'] = payment;
    data['installment'] = installment;
    // Note: ID is not included in JSON as it's only for UI purposes
    return data;
  }

  PricePlanDataItem copyWith({
    String? date,
    String? order,
    int? payment,
    String? installment,
    String? id,
  }) {
    return PricePlanDataItem(
      date: date ?? this.date,
      order: order ?? this.order,
      payment: payment ?? this.payment,
      installment: installment ?? this.installment,
      id: id ?? this.id,
    );
  }

  @override
  List<Object?> get props => [date, order, payment, installment, id];
}

class PricePlanModel extends Equatable {
  final int? id;
  final String? name;
  final String? nameAr;
  final String? nameEn;
  final List<PricePlanDataItem>? data;
  final String? description;
  final String? descriptionAr;
  final String? descriptionEn;

  const PricePlanModel({
    this.id,
    this.name,
    this.nameAr,
    this.nameEn,
    this.data,
    this.description,
    this.descriptionAr,
    this.descriptionEn,
  });

  factory PricePlanModel.fromJson(Map<String?, dynamic> json) {
    return PricePlanModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      nameAr: json['name_ar'] as String?,
      nameEn: json['name_en'] as String?,
      data: json['data'] != null
          ? (json['data'] as List)
              .map((e) => PricePlanDataItem.fromJson(e))
              .toList()
          : null,
      description: json['description'] as String?,
      descriptionAr: json['description_ar'] as String?,
      descriptionEn: json['description_en'] as String?,
    );
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['name_ar'] = nameAr;
    data['name_en'] = nameEn;
    if (this.data != null) {
      data['data'] = this.data!.map((e) => e.toJson()).toList();
    }
    data['description'] = description;
    data['description_ar'] = descriptionAr;
    data['description_en'] = descriptionEn;
    return data;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        nameAr,
        nameEn,
        data,
        description,
        descriptionAr,
        descriptionEn
      ];
}
