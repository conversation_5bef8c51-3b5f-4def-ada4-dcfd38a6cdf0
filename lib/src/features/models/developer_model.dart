import 'package:equatable/equatable.dart';

class DeveloperModel extends Equatable {
  final int? id;
  final String? name;
  final String? nameAr;
  final String? nameEn;
  final String? description;
  final String? descriptionAr;
  final String? descriptionEn;
  final String? developerLogo;

  const DeveloperModel({
    this.id,
    this.name,
    this.nameAr,
    this.nameEn,
    this.description,
    this.descriptionAr,
    this.descriptionEn,
    this.developerLogo,
  });

  factory DeveloperModel.fromJson(Map<String, dynamic> json) {
    return DeveloperModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      nameAr: json['name_ar'] as String?,
      nameEn: json['name_en'] as String?,
      description: json['description'] as String?,
      descriptionAr: json['description_ar'] as String?,
      descriptionEn: json['description_en'] as String?,
      developerLogo: json['developer_logo'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['name_ar'] = nameAr;
    data['name_en'] = nameEn;
    data['description'] = description;
    data['description_ar'] = descriptionAr;
    data['description_en'] = descriptionEn;
    data['developer_logo'] = developerLogo;
    return data;
  }

  DeveloperModel copyWith({
    int? id,
    String? name,
    String? nameAr,
    String? nameEn,
    String? description,
    String? descriptionAr,
    String? descriptionEn,
    String? developerLogo,
  }) {
    return DeveloperModel(
      id: id ?? this.id,
      name: name ?? this.name,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      descriptionEn: descriptionEn ?? this.descriptionEn,
      developerLogo: developerLogo ?? this.developerLogo,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        nameAr,
        nameEn,
        description,
        descriptionAr,
        descriptionEn,
        developerLogo,
      ];
}
