import 'package:equatable/equatable.dart';

import '../models/developer_model.dart';

class DeveloperResponse extends Equatable {
  final List<DeveloperModel> results;
  final String? error;
  final int? code;
  final String? msg;
  final bool? status;
  
  const DeveloperResponse(this.results, this.error, this.code, this.msg, this.status);

  DeveloperResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = (parsedJson['data'] as List)
            .map((p) => DeveloperModel.fromJson(p))
            .toList(),
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['message'],
        status = parsedJson['status'];

  DeveloperResponse.withError(String? errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail",
        status = false;

  @override
  List<Object?> get props => [results, error, code, msg, status];
}

class DeveloperDetailsResponse extends Equatable {
  final DeveloperModel? data;
  final String? error;
  final int? code;
  final String? msg;
  final bool? status;
  
  const DeveloperDetailsResponse(this.data, this.error, this.code, this.msg, this.status);

  DeveloperDetailsResponse.fromJson(Map<String, dynamic> parsedJson)
      : data = parsedJson['data'] != null 
            ? DeveloperModel.fromJson(parsedJson['data'])
            : null,
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['message'],
        status = parsedJson['status'];

  DeveloperDetailsResponse.withError(String? errorValue)
      : data = null,
        error = errorValue,
        code = 0,
        msg = "fail",
        status = false;

  @override
  List<Object?> get props => [data, error, code, msg, status];
}
